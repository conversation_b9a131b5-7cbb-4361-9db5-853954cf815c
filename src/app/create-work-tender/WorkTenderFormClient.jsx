"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "../../context/AuthContext";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderFormContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  width: 100%;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }
`;
Input.displayName = "Input";

const FormGroup = styled.div`
  margin-bottom: 24px;
`;
FormGroup.displayName = "FormGroup";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const TenderNameInput = styled(Input)`
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TenderNameInput.displayName = "TenderNameInput";

// Стили для выпадающего списка городов
const CityDropdown = styled.div`
  position: relative;
  width: 100%;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #434a54;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }

  &:hover {
    border-color: #0066cc;
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #434a54;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

// Стили для кнопок выбора НДС
const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

// Стили для календаря
const DatePickerContainer = styled.div`
  background: #f5f5f5;
  padding: 48px 0;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
DatePickerContainer.displayName = "DatePickerContainer";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

// Стили для загрузки файлов
const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 16px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #666;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5c6cb;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const PublishButton = styled(CreateTenderButton)`
  font-size: 16px;
  padding: 16px 32px;
  margin-top: 32px;
`;
PublishButton.displayName = "PublishButton";

const VatSection = styled.div`
  margin-bottom: 32px;
`;
VatSection.displayName = "VatSection";

const VatText = styled.p`
  font-size: 14px;
  color: #969ea7;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
VatText.displayName = "VatText";

const WorkTenderFormClient = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderName, setTenderName] = useState("");
  const [deliveryDate, setDeliveryDate] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [selectedCityId, setSelectedCityId] = useState("02");
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [description, setDescription] = useState("");
  const [vatIncluded, setVatIncluded] = useState(true);
  const [attachedFiles, setAttachedFiles] = useState([]);

  // Список городов
  const cities = [
    { RegionId: "01", RegionName: "Нур-Султан" },
    { RegionId: "02", RegionName: "Алматы" },
    { RegionId: "04", RegionName: "Актюбинская область" },
    { RegionId: "05", RegionName: "Алматинская область" },
    { RegionId: "06", RegionName: "Атырауская область" },
    { RegionId: "07", RegionName: "Западно-Казахстанская область" },
    { RegionId: "08", RegionName: "Жамбылская область" },
    { RegionId: "09", RegionName: "Карагандинская область" },
    { RegionId: "10", RegionName: "Костанайская область" },
    { RegionId: "11", RegionName: "Кызылординская область" },
    { RegionId: "12", RegionName: "Мангистауская область" },
    { RegionId: "13", RegionName: "Туркестанская область" },
    { RegionId: "14", RegionName: "Павлодарская область" },
    { RegionId: "15", RegionName: "Северо-Казахстанская область" },
    { RegionId: "16", RegionName: "Восточно-Казахстанская область" },
    { RegionId: "17", RegionName: "Акмолинская область" },
    { RegionId: "3 ", RegionName: "Шымкент" },
  ];

  const handleBack = () => {
    router.back();
  };

  const handleClearAll = () => {
    setTenderName("");
    setDeliveryDate(null);
    setDeliveryAddress("");
    setDescription("");
    setAttachedFiles([]);
  };

  // Функции для работы с dropdown городов
  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleCitySelect = (selectedCity) => {
    setSelectedCityId(selectedCity.RegionId);
    setIsCityDropdownOpen(false);
  };

  // Получаем текущий выбранный город
  const getCurrentCity = () => {
    return cities.find((city) => city.RegionId === selectedCityId) || cities[1];
  };

  const handleVatChoice = (includeVat) => {
    setVatIncluded(includeVat);
  };

  // Закрытие dropdown при клике вне его
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isCityDropdownOpen && !event.target.closest("[data-city-dropdown]")) {
        setIsCityDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCityDropdownOpen]);

  // Функции для работы с файлами
  const handleFileUpload = (files) => {
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      // Проверяем размер файлов (максимум 10MB на файл)
      const maxSize = 10 * 1024 * 1024; // 10MB
      const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

      if (oversizedFiles.length > 0) {
        alert(
          `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
            .map((f) => f.name)
            .join("\n")}`
        );
        return;
      }

      setAttachedFiles((prev) => [...prev, ...fileArray]);
    }
  };

  const handleRemoveFile = (fileIndex) => {
    setAttachedFiles((prev) => prev.filter((_, index) => index !== fileIndex));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleSubmit = () => {
    // Валидация обязательных полей
    if (!tenderName.trim()) {
      alert("Укажите название тендера");
      return;
    }

    if (!deliveryDate) {
      alert("Укажите срок выполнения работ");
      return;
    }

    if (!deliveryAddress.trim()) {
      alert("Укажите адрес выполнения работ");
      return;
    }

    if (!description.trim()) {
      alert("Укажите описание работ/услуг");
      return;
    }

    // Здесь будет логика отправки данных на сервер
    console.log("Данные тендера на работы/услуги:", {
      tenderName,
      deliveryDate,
      deliveryAddress,
      selectedCityId,
      description,
      vatIncluded,
      attachedFiles: attachedFiles.length,
    });

    alert("Тендер на работы/услуги будет создан (функция в разработке)");
  };

  return (
    <>
      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img src="/icons/arrow_back_24px.svg" alt="Назад" />
            ВЕРНУТЬСЯ К ПОИСКУ
          </BackButton>

          <ActionButtons>
            <ClearAllButton onClick={handleClearAll}>
              <img
                src="/icons/BusketCreateTender.svg"
                width={"13"}
                height={"13"}
                alt="Очистить"
              />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton>
          </ActionButtons>
        </HeaderContent>
      </Header>

      <TenderFormContainer>
        <ContentContainer>
          <Title>Создание тендера на работы/услуги</Title>

          <SectionTitle>Основная информация</SectionTitle>
          <Text>
            Укажите название тендера, описание работ/услуг и необходимые сроки
            выполнения.
          </Text>

          <FormGroup>
            <Label>Наименование тендера*</Label>
            <TenderNameInput
              type="text"
              value={tenderName}
              onChange={(e) => setTenderName(e.target.value)}
              placeholder="Введите название тендера"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Описание работ/услуг*</Label>
            <TextArea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Подробно опишите требуемые работы или услуги"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Регион выполнения работ*</Label>
            <CityDropdown data-city-dropdown>
              <CityButton onClick={toggleCityDropdown}>
                <span>{getCurrentCity().RegionName}</span>
                <span>{isCityDropdownOpen ? "▲" : "▼"}</span>
              </CityButton>
              {isCityDropdownOpen && (
                <CityDropdownList>
                  {cities.map((city) => (
                    <CityOption
                      key={city.RegionId}
                      onClick={() => handleCitySelect(city)}
                    >
                      {city.RegionName}
                    </CityOption>
                  ))}
                </CityDropdownList>
              )}
            </CityDropdown>
          </FormGroup>

          <FormGroup>
            <Label>Адрес выполнения работ*</Label>
            <TenderNameInput
              type="text"
              value={deliveryAddress}
              onChange={(e) => setDeliveryAddress(e.target.value)}
              placeholder="Укажите точный адрес выполнения работ"
              required
            />
          </FormGroup>

          <VatSection>
            <SectionTitle>Цена с НДС или без НДС?</SectionTitle>
            <VatText>
              Укажите, должны ли поставщики включать НДС в свои предложения.
            </VatText>

            <ActionButtonContainer>
              <ActionButton
                active={vatIncluded === true}
                onClick={() => handleVatChoice(true)}
              >
                Цена с НДС
              </ActionButton>
              <ActionButton
                active={vatIncluded === false}
                onClick={() => handleVatChoice(false)}
              >
                Цена без НДС
              </ActionButton>
            </ActionButtonContainer>
          </VatSection>

          <DatePickerContainer>
            <SectionTitle>Срок выполнения работ</SectionTitle>
            <Text>Укажите дату когда необходимо завершить работы</Text>

            <CalendarContainer>
              <DatePicker
                selected={deliveryDate}
                onChange={(date) => setDeliveryDate(date)}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                minDate={new Date()}
                placeholderText="Выберите дату"
                inline
              />
            </CalendarContainer>
          </DatePickerContainer>

          <FormGroup>
            <Label>Прикрепить файлы</Label>
            <UploadButton
              onClick={() => document.getElementById("file-input").click()}
            >
              <img src="/icons/Upload.svg" alt="Загрузить" />
              <UploadText>Прикрепить файл</UploadText>
            </UploadButton>

            <HiddenFileInput
              id="file-input"
              type="file"
              multiple
              onChange={(e) => handleFileUpload(e.target.files)}
            />

            {attachedFiles.length > 0 && (
              <AttachedFilesList>
                {attachedFiles.map((file, index) => (
                  <AttachedFileItem key={index}>
                    <FileInfo>
                      <FileName>{file.name}</FileName>
                      <FileSize>{formatFileSize(file.size)}</FileSize>
                    </FileInfo>
                    <RemoveFileButton
                      onClick={() => handleRemoveFile(index)}
                      title="Удалить файл"
                    >
                      ×
                    </RemoveFileButton>
                  </AttachedFileItem>
                ))}
              </AttachedFilesList>
            )}
          </FormGroup>

          <PublishButton onClick={handleSubmit}>
            СОЗДАТЬ ТЕНДЕР НА РАБОТЫ/УСЛУГИ
          </PublishButton>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default WorkTenderFormClient;
