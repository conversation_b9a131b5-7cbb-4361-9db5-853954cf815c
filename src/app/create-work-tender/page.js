import { Suspense } from "react";
import Layout from "../../components/Layout";
import CreateWorkTenderClient from "./CreateWorkTenderClient";
import { ISRUtils } from "../../config/isr";

// Генерация мета-тегов для SEO
export async function generateMetadata() {
  return {
    title:
      "Создание тендера на выполнение работ | SADI Shop - строительные и монтажные работы",
    description:
      "Создайте тендер на выполнение строительных и монтажных работ в SADI Shop. Найдите подрядчиков, укажите объем работ, сроки выполнения и опубликуйте тендер для исполнителей в Казахстане.",
    keywords: [
      "SADI Shop",
      "САДИ Шоп",
      "sadi",
      "сади",
      "создать тендер на работы",
      "тендер на выполнение работ",
      "строительные работы",
      "монтажные работы",
      "подрядчики",
      "исполнители работ",
      "строительство",
      "ремонт",
      "отделочные работы",
      "электромонтажные работы",
      "сантехнические работы",
      "кровельные работы",
      "фасадные работы",
      "земляные работы",
      "бетонные работы",
      "каменные работы",
      "штукатурные работы",
      "малярные работы",
      "плиточные работы",
      "напольные покрытия",
      "потолочные работы",
      "оконные работы",
      "дверные работы",
      "изоляционные работы",
      "гидроизоляция",
      "теплоизоляция",
      "звукоизоляция",
      "вентиляция",
      "кондиционирование",
      "отопление",
      "водоснабжение",
      "канализация",
      "электрика",
      "слаботочные системы",
      "охранные системы",
      "пожарная сигнализация",
      "видеонаблюдение",
      "автоматизация",
      "умный дом",
      "ландшафтные работы",
      "благоустройство",
      "дорожные работы",
      "асфальтирование",
      "тротуарная плитка",
      "ограждения",
      "заборы",
      "ворота",
      "навесы",
      "беседки",
      "террасы",
      "балконы",
      "лоджии",
      "мансарды",
      "подвалы",
      "фундаменты",
      "стены",
      "перекрытия",
      "лестницы",
      "крыши",
      "чердаки",
      "гаражи",
      "сараи",
      "бани",
      "сауны",
      "бассейны",
      "пруды",
      "фонтаны",
      "альпийские горки",
      "газоны",
      "клумбы",
      "деревья",
      "кустарники",
      "цветы",
      "полив",
      "дренаж",
      "освещение участка",
      "садовые дорожки",
      "детские площадки",
      "спортивные площадки",
      "парковки",
      "подъездные пути",
      "мостики",
      "подпорные стенки",
      "террасирование",
      "планировка участка",
      "геодезические работы",
      "топографическая съемка",
      "проектирование",
      "архитектура",
      "дизайн",
      "интерьер",
      "экстерьер",
      "3D моделирование",
      "визуализация",
      "чертежи",
      "схемы",
      "планы",
      "сметы",
      "расчеты",
      "техническое задание",
      "авторский надзор",
      "строительный контроль",
      "технический надзор",
      "экспертиза",
      "обследование зданий",
      "диагностика",
      "реконструкция",
      "капитальный ремонт",
      "текущий ремонт",
      "косметический ремонт",
      "евроремонт",
      "элитный ремонт",
      "бюджетный ремонт",
      "быстрый ремонт",
      "качественный ремонт",
      "гарантийный ремонт",
      "послегарантийное обслуживание",
      "сервисное обслуживание",
      "техническое обслуживание",
      "эксплуатация зданий",
      "управление недвижимостью",
      "клининг",
      "уборка",
      "мойка окон",
      "химчистка",
      "дезинфекция",
      "дератизация",
      "дезинсекция",
      "озеленение",
      "уход за растениями",
      "стрижка газонов",
      "обрезка деревьев",
      "удаление деревьев",
      "корчевание пней",
      "вывоз мусора",
      "утилизация отходов",
      "демонтаж",
      "снос зданий",
      "разборка конструкций",
      "погрузочно-разгрузочные работы",
      "транспортировка",
      "доставка материалов",
      "складские услуги",
      "хранение",
      "охрана объектов",
      "безопасность",
      "пропускной режим",
      "видеонаблюдение",
      "сигнализация",
      "пожарная безопасность",
      "охрана труда",
      "промышленная безопасность",
      "экологическая безопасность",
      "Казахстан",
      "Астана",
      "Нур-Султан",
      "Алматы",
      "Шымкент",
      "Караганда",
      "Актобе",
      "Тараз",
      "Павлодар",
      "Усть-Каменогорск",
      "Семей",
      "Атырау",
      "Костанай",
      "Кызылорда",
      "Уральск",
      "Петропавловск",
      "Актау",
      "Темиртау",
      "Туркестан",
      "Кокшетау",
      "Талдыкорган",
      "Экибастуз",
      "Рудный",
      "Жезказган",
      "Балхаш",
    ],
    openGraph: {
      title: "Создание тендера на выполнение работ",
      description:
        "Создайте тендер на выполнение строительных и монтажных работ. Найдите квалифицированных подрядчиков и исполнителей.",
      type: "website",
      url: "https://shop.sadi.kz/create-work-tender",
      siteName: "SADI.KZ",
      images: [
        {
          url: "/images/work-tender-og.jpg",
          width: 1200,
          height: 630,
          alt: "Создание тендера на выполнение работ",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Создание тендера на выполнение работ",
      description:
        "Создайте тендер на выполнение строительных и монтажных работ.",
      images: ["/images/work-tender-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: "https://shop.sadi.kz/create-work-tender",
    },
  };
}

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Создание тендера на выполнение работ",
  description: "Создайте тендер на выполнение строительных и монтажных работ",
  url: "https://shop.sadi.kz/create-work-tender",
  mainEntity: {
    "@type": "Service",
    name: "Создание тендера на работы",
    description:
      "Сервис для создания тендеров на выполнение строительных и монтажных работ",
    provider: {
      "@type": "Organization",
      name: "SADI.KZ",
      url: "https://shop.sadi.kz",
    },
    serviceType: "Электронные торги на работы",
    areaServed: {
      "@type": "Country",
      name: "Казахстан",
    },
  },
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Создание тендера на работы",
        item: "https://shop.sadi.kz/create-work-tender",
      },
    ],
  },
};

// Компонент загрузки
function CreateWorkTenderLoading() {
  return (
    <div
      style={{
        padding: "40px 20px",
        textAlign: "center",
        minHeight: "400px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div>
        <h1>Создание тендера на работы</h1>
        <p>Загрузка каталога работ...</p>
        <div
          style={{
            width: "40px",
            height: "40px",
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #0066cc",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "20px auto",
          }}
        />
      </div>
    </div>
  );
}

// ISR: Кэширование страницы создания тендера на работы на 7 дней
export const revalidate = 604800; // 7 дней в секундах

// Основной серверный компонент
export default function CreateWorkTenderPage() {
  // Логируем ISR операцию
  ISRUtils.logISROperation("create-work-tender-page", {
    revalidate: 604800,
    note: "Страница создания тендера на работы с ISR кэшированием",
  });

  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO-контент для поисковых систем */}
      <div style={{ display: "none" }}>
        <h1>Создание тендера на выполнение строительных и монтажных работ</h1>
        <h2>Как создать тендер на выполнение работ</h2>
        <p>
          Создание тендера на выполнение работ на нашей платформе - это простой
          и удобный способ найти лучших подрядчиков и исполнителей строительных,
          монтажных и отделочных работ. Следуйте простым шагам: найдите нужные
          работы, добавьте их в список, укажите объемы и требования, сроки
          выполнения, опубликуйте тендер.
        </p>
        <h3>Преимущества создания тендера на работы</h3>
        <ul>
          <li>Конкурентные цены от множества подрядчиков</li>
          <li>Экономия времени на поиск исполнителей</li>
          <li>Прозрачный процесс выбора подрядчика</li>
          <li>Гарантия качества выполнения работ</li>
          <li>Квалифицированные специалисты</li>
          <li>Современное оборудование и технологии</li>
        </ul>
      </div>

      {/* Клиентский компонент с Suspense */}
      <Suspense fallback={<CreateWorkTenderLoading />}>
        <CreateWorkTenderClient />
      </Suspense>
    </Layout>
  );
}
