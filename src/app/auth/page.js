"use client";

import React, { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import Layout from "../../components/Layout";
import { useAuth } from "../../context/AuthContext";
import RegistrationWizard from "../../components/RegistrationWizard";
import { useSearchParams } from "next/navigation";
import API_CONFIG from "../../config/api";
import authService from "../../services/auth.service";

const AuthContainer = styled.div`
  padding: 24px;
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 120px);
  background-color: #f8f9fa;

  @media (max-width: 768px) {
    padding: 16px;
    min-height: calc(100vh - 80px);
  }
`;
AuthContainer.displayName = "AuthContainer";

const AuthCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 8px;
  }
`;

// Отдельный стиль для личного кабинета
const ProfileCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 32px;
  width: 100%;
  max-width: 1200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 8px;
  }
`;
AuthCard.displayName = "AuthCard";

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 32px;
  border-bottom: 1px solid #e9ecef;
`;
TabContainer.displayName = "TabContainer";

const Tab = styled.button`
  flex: 1;
  padding: 12px 0;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: ${(props) => (props.active ? "#0066cc" : "#6c757d")};
  border-bottom: 2px solid
    ${(props) => (props.active ? "#0066cc" : "transparent")};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #0066cc;
  }
`;
Tab.displayName = "Tab";

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;
Form.displayName = "Form";

const InputField = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: #f8f9fa;
  transition: all 0.2s ease;

  &::placeholder {
    color: #adb5bd;
  }

  &:focus {
    outline: none;
    border-color: #0066cc;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
  }
`;
InputField.displayName = "InputField";

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;
CheckboxContainer.displayName = "CheckboxContainer";

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: #0066cc;
`;
Checkbox.displayName = "Checkbox";

const CheckboxLabel = styled.label`
  font-size: 14px;
  color: #6c757d;
  cursor: pointer;
`;
CheckboxLabel.displayName = "CheckboxLabel";

const ErrorMessage = styled.div`
  color: #dc3545;
  font-size: 14px;
  text-align: center;
  padding: 8px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
`;
ErrorMessage.displayName = "ErrorMessage";

const SubmitButton = styled.button`
  width: 100%;
  padding: 14px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover:not(:disabled) {
    background-color: #0055b3;
  }

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }
`;
SubmitButton.displayName = "SubmitButton";

// Стили для уведомления об авторизации
const AuthNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  max-width: 90%;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
AuthNotification.displayName = "AuthNotification";

const AuthNotificationText = styled.span`
  flex: 1;
`;
AuthNotificationText.displayName = "AuthNotificationText";

const AuthNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
AuthNotificationClose.displayName = "AuthNotificationClose";

// Стили для редактируемых полей
const ProfileInput = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
  }

  &:disabled {
    background-color: #f8f9fa;
    color: #666;
  }
`;
ProfileInput.displayName = "ProfileInput";

const ProfileTextarea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 80px;

  &:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
  }

  &:disabled {
    background-color: #f8f9fa;
    color: #666;
  }
`;
ProfileTextarea.displayName = "ProfileTextarea";

const FieldGroup = styled.div`
  margin-bottom: 16px;
`;
FieldGroup.displayName = "FieldGroup";

const FieldLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
`;
FieldLabel.displayName = "FieldLabel";

const ReadOnlyField = styled.div`
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
`;
ReadOnlyField.displayName = "ReadOnlyField";

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;
ButtonGroup.displayName = "ButtonGroup";

// Стили для макета с колонками
const ProfileGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 24px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`;
ProfileGrid.displayName = "ProfileGrid";

const ProfileSection = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e9ecef;

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 8px;
  }
`;
ProfileSection.displayName = "ProfileSection";

const SystemInfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
`;
SystemInfoGrid.displayName = "SystemInfoGrid";

const CompactFieldGroup = styled.div`
  margin-bottom: 12px;
`;
CompactFieldGroup.displayName = "CompactFieldGroup";

const CompactFieldLabel = styled.label`
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
CompactFieldLabel.displayName = "CompactFieldLabel";

const CompactReadOnlyField = styled.div`
  padding: 6px 8px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  font-weight: 500;
  min-height: 20px;
`;
CompactReadOnlyField.displayName = "CompactReadOnlyField";

const SaveButton = styled.button`
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #218838;
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }
`;
SaveButton.displayName = "SaveButton";

// Стили для уведомлений
const SuccessNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #28a745;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
SuccessNotification.displayName = "SuccessNotification";

const SuccessNotificationText = styled.span`
  flex: 1;
`;
SuccessNotificationText.displayName = "SuccessNotificationText";

const SuccessNotificationClose = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;
SuccessNotificationClose.displayName = "SuccessNotificationClose";

// Стили для выпадающего списка
const DropdownContainer = styled.div`
  position: relative;
  width: 100%;
`;
DropdownContainer.displayName = "DropdownContainer";

const DropdownButton = styled.button`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  text-align: left;
  cursor: pointer;
  transition: border-color 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
  }

  &:hover {
    border-color: #0066cc;
  }

  &:disabled {
    background-color: #f8f9fa;
    color: #666;
    cursor: not-allowed;
  }
`;
DropdownButton.displayName = "DropdownButton";

const DropdownArrow = styled.span`
  font-size: 12px;
  color: #666;
  transition: transform 0.2s ease;
  transform: ${(props) => (props.isOpen ? "rotate(180deg)" : "rotate(0deg)")};
`;
DropdownArrow.displayName = "DropdownArrow";

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
`;
DropdownMenu.displayName = "DropdownMenu";

const DropdownOption = styled.div`
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  &.selected {
    background-color: #e3f2fd;
    color: #0066cc;
    font-weight: 500;
  }
`;
DropdownOption.displayName = "DropdownOption";

// Компонент выпадающего списка
const Dropdown = ({
  value,
  options,
  onChange,
  placeholder = "Выберите...",
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Закрываем выпадающий список при клике вне его
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const selectedOption = options.find((option) => option.id === value);
  const displayText = selectedOption ? selectedOption.name : placeholder;

  const handleOptionClick = (option) => {
    onChange(option.id);
    setIsOpen(false);
  };

  return (
    <DropdownContainer ref={dropdownRef}>
      <DropdownButton
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <span>{displayText}</span>
        <DropdownArrow isOpen={isOpen}>▼</DropdownArrow>
      </DropdownButton>

      {isOpen && !disabled && (
        <DropdownMenu>
          {options.map((option) => (
            <DropdownOption
              key={option.id}
              className={value === option.id ? "selected" : ""}
              onClick={() => handleOptionClick(option)}
            >
              {option.name}
            </DropdownOption>
          ))}
        </DropdownMenu>
      )}
    </DropdownContainer>
  );
};

const ErrorNotification = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
  max-width: 500px;
  animation: slideDown 0.3s ease-out;

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 768px) {
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 14px;
    padding: 12px 20px;
    max-width: 95%;
  }
`;
ErrorNotification.displayName = "ErrorNotification";

// Компонент личного кабинета
const UserProfile = () => {
  const { user, logout, refreshUserData } = useAuth();
  const [editableData, setEditableData] = useState({});
  const [originalData, setOriginalData] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Состояния для уведомлений
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");

  // Данные для выпадающих списков
  const uTypeOptions = [
    { id: 1, name: "Физическое лицо" },
    { id: 2, name: "Юридическое лицо" },
  ];

  const cTypeOptions = [
    { id: 1, name: "ИП (Индивидуальное предпринимательство)" },
    { id: 2, name: "ТОО (Товарищество с ограниченной ответственностью)" },
    { id: 3, name: "АО (Акционерное общество)" },
    { id: 4, name: "ГП (Государственное предприятие)" },
  ];

  const businessScopeOptions = [
    { id: 1, name: "Строительная компания" },
    { id: 2, name: "Грузоперевозка" },
    { id: 3, name: "Поставщик строительных материалов" },
    { id: 4, name: "Проектировщик" },
    { id: 5, name: "Сметчик" },
  ];

  const regionOptions = [
    { id: "01", name: "Нур-Султан" },
    { id: "02", name: "Алматы" },
    { id: "04", name: "Актюбинская область" },
    { id: "05", name: "Алматинская область" },
    { id: "06", name: "Атырауская область" },
    { id: "07", name: "Западно-Казахстанская область" },
    { id: "08", name: "Жамбылская область" },
    { id: "09", name: "Карагандинская область" },
    { id: "10", name: "Костанайская область" },
    { id: "11", name: "Кызылординская область" },
    { id: "12", name: "Мангистауская область" },
    { id: "13", name: "Туркестанская область" },
    { id: "14", name: "Павлодарская область" },
    { id: "15", name: "Северо-Казахстанская область" },
    { id: "16", name: "Восточно-Казахстанская область" },
    { id: "17", name: "Акмолинская область" },
    { id: "3 ", name: "Шымкент" },
  ];

  // Инициализируем редактируемые данные при загрузке пользователя
  useEffect(() => {
    if (user) {
      const initialData = {
        lastName: user.lastName || "",
        firstName: user.firstName || "",
        middleName: user.middleName || "",
        position: user.position || "",
        phone: user.phone || "",
        email: user.email || "",
        companyName: user.companyName || "",
        legalAddress: user.legalAddress || "",
        actualAddress: user.actualAddress || "",
        site: user.site || "",
        fax: user.fax || "",
        description: user.description || "",
        bin: user.bin || "",
        kbe: user.kbe || "",
        iik: user.iik || "",
        bankName: user.bankName || "",
        bik: user.bik || "",
        // Добавляем поля для выпадающих списков
        uTypeId: user.uTypeId || null,
        cTypeId: user.cTypeId || null,
        businessScopeId: user.businessScopeId || null,
        regionId: user.regionId || null,
      };
      setEditableData(initialData);
      setOriginalData(initialData);
      setHasChanges(false);
    }
  }, [user]);

  // Обновляем данные пользователя при загрузке компонента
  useEffect(() => {
    const updateUserData = async () => {
      if (user?.email) {
        console.log("🔄 Обновление данных пользователя в личном кабинете...");
        await refreshUserData();
      }
    };

    updateUserData();
  }, []); // Выполняется только при первой загрузке компонента

  // Функции для управления уведомлениями
  const showSuccess = (message) => {
    setNotificationMessage(message);
    setShowSuccessNotification(true);
    setTimeout(() => {
      setShowSuccessNotification(false);
    }, 3000);
  };

  const showError = (message) => {
    setNotificationMessage(message);
    setShowErrorNotification(true);
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  };

  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
  };

  const handleInputChange = (field, value) => {
    // Валидация для поля КБЕ
    if (field === "kbe") {
      // Разрешаем только цифры и максимум 2 символа
      const numericValue = value.replace(/[^0-9]/g, "");
      value = numericValue.slice(0, 2);
    }

    const newData = {
      ...editableData,
      [field]: value,
    };

    setEditableData(newData);

    // Проверяем, есть ли изменения по сравнению с оригинальными данными
    const hasAnyChanges = Object.keys(newData).some(
      (key) => newData[key] !== originalData[key]
    );

    setHasChanges(hasAnyChanges);
  };

  const handleSave = async () => {
    if (!hasChanges || isSaving) return;

    setIsSaving(true);

    try {
      // Определяем только измененные поля (исключаем email и bin)
      const changedFields = {};
      const readOnlyFields = ["email", "bin"]; // Поля, которые нельзя изменять

      Object.keys(editableData).forEach((key) => {
        if (
          !readOnlyFields.includes(key) &&
          editableData[key] !== originalData[key]
        ) {
          changedFields[key] = editableData[key];
        }
      });

      // Маппинг полей формы к полям API
      const apiFieldMapping = {
        lastName: "LastName",
        firstName: "FirstName",
        middleName: "MiddleName",
        position: "Position",
        phone: "Phone",
        email: "Email",
        companyName: "CompanyName",
        legalAddress: "LegalAddress",
        actualAddress: "ActualAddress",
        site: "Site",
        fax: "Fax",
        description: "Description",
        bin: "Bin",
        kbe: "KBE",
        iik: "IIK",
        bankName: "BankName",
        bik: "BIK",
        // Добавляем новые поля для выпадающих списков
        uTypeId: "UTypeId",
        cTypeId: "CTypeId",
        businessScopeId: "BusinessScopeId",
        regionId: "RegionId",
      };

      // Формируем тело запроса с измененными полями
      const requestBody = {
        UserId: user.userId,
      };

      // Добавляем только измененные поля с правильными именами для API
      Object.keys(changedFields).forEach((key) => {
        const apiFieldName = apiFieldMapping[key];
        if (apiFieldName) {
          requestBody[apiFieldName] = changedFields[key];
        }
      });

      console.log("Отправляем PATCH запрос с данными:", requestBody);

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/PUsers`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authService.currentToken}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        console.log("✅ Данные пользователя успешно обновлены");

        // Обновляем данные пользователя из API для синхронизации
        await refreshUserData();

        // Обновляем оригинальные данные
        setOriginalData(editableData);
        setHasChanges(false);

        // Показываем уведомление об успехе
        showSuccess("Данные успешно сохранены!");
      } else {
        const errorData = await response.json();
        console.error("❌ Ошибка при обновлении данных:", errorData);
        showError(
          `Ошибка при сохранении: ${errorData.message || "Неизвестная ошибка"}`
        );
      }
    } catch (error) {
      console.error("❌ Ошибка при отправке PATCH запроса:", error);
      showError("Произошла ошибка при сохранении данных. Попробуйте еще раз.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <>
      {/* Уведомления */}
      {showSuccessNotification && (
        <SuccessNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseSuccessNotification}>
            ×
          </SuccessNotificationClose>
        </SuccessNotification>
      )}

      {showErrorNotification && (
        <ErrorNotification>
          <SuccessNotificationText>
            {notificationMessage}
          </SuccessNotificationText>
          <SuccessNotificationClose onClick={handleCloseErrorNotification}>
            ×
          </SuccessNotificationClose>
        </ErrorNotification>
      )}

      <AuthContainer>
        <ProfileCard>
          <h2
            style={{ marginBottom: "24px", textAlign: "center", color: "#333" }}
          >
            Личный кабинет
          </h2>

          <ProfileSection style={{ marginBottom: "24px" }}>
            <h3>Информация о пользователе</h3>

            <SystemInfoGrid>
              <CompactFieldGroup>
                <CompactFieldLabel>Тип пользователя</CompactFieldLabel>
                <Dropdown
                  value={editableData.uTypeId}
                  options={uTypeOptions}
                  onChange={(value) => handleInputChange("uTypeId", value)}
                  placeholder="Выберите тип пользователя"
                />
              </CompactFieldGroup>

              <CompactFieldGroup>
                <CompactFieldLabel>Тип компании</CompactFieldLabel>
                <Dropdown
                  value={editableData.cTypeId}
                  options={cTypeOptions}
                  onChange={(value) => handleInputChange("cTypeId", value)}
                  placeholder="Выберите тип компании"
                />
              </CompactFieldGroup>

              <CompactFieldGroup>
                <CompactFieldLabel>Вид деятельности</CompactFieldLabel>
                <Dropdown
                  value={editableData.businessScopeId}
                  options={businessScopeOptions}
                  onChange={(value) =>
                    handleInputChange("businessScopeId", value)
                  }
                  placeholder="Выберите вид деятельности"
                />
              </CompactFieldGroup>

              <CompactFieldGroup>
                <CompactFieldLabel>Регион</CompactFieldLabel>
                <Dropdown
                  value={editableData.regionId}
                  options={regionOptions}
                  onChange={(value) => handleInputChange("regionId", value)}
                  placeholder="Выберите регион"
                />
              </CompactFieldGroup>
            </SystemInfoGrid>
          </ProfileSection>

          {/* Основные формы в две колонки */}
          <ProfileGrid>
            {/* Левая колонка - Личная информация */}
            <ProfileSection>
              <h3>Личная информация</h3>

              <FieldGroup>
                <FieldLabel>Фамилия</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  placeholder="Введите фамилию"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Имя</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  placeholder="Введите имя"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Отчество</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.middleName}
                  onChange={(e) =>
                    handleInputChange("middleName", e.target.value)
                  }
                  placeholder="Введите отчество"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Должность</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.position}
                  onChange={(e) =>
                    handleInputChange("position", e.target.value)
                  }
                  placeholder="Введите должность"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Телефон</FieldLabel>
                <ProfileInput
                  type="tel"
                  value={editableData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="Введите телефон"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Email</FieldLabel>
                <ProfileInput
                  type="email"
                  value={editableData.email}
                  readOnly
                  disabled
                  style={{ backgroundColor: "#f5f5f5", cursor: "not-allowed" }}
                  placeholder="Введите email"
                />
                <small style={{ color: "#6c757d", marginTop: "4px" }}>
                  Email нельзя изменить
                </small>
              </FieldGroup>
            </ProfileSection>

            {/* Правая колонка - Банковская информация */}
            <ProfileSection>
              <h3>Банковская информация</h3>

              <FieldGroup>
                <FieldLabel>КБЕ</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.kbe}
                  onChange={(e) => handleInputChange("kbe", e.target.value)}
                  placeholder="Введите КБЕ (только цифры, макс. 2)"
                  maxLength="2"
                  pattern="[0-9]*"
                  inputMode="numeric"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>ИИК</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.iik}
                  onChange={(e) => handleInputChange("iik", e.target.value)}
                  placeholder="Введите ИИК"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>Наименование банка</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.bankName}
                  onChange={(e) =>
                    handleInputChange("bankName", e.target.value)
                  }
                  placeholder="Введите наименование банка"
                />
              </FieldGroup>

              <FieldGroup>
                <FieldLabel>БИК</FieldLabel>
                <ProfileInput
                  type="text"
                  value={editableData.bik}
                  onChange={(e) => handleInputChange("bik", e.target.value)}
                  placeholder="Введите БИК"
                />
              </FieldGroup>
            </ProfileSection>
          </ProfileGrid>

          {/* Информация о компании - полная ширина */}
          <ProfileSection style={{ marginBottom: "24px" }}>
            <h3>Информация о компании</h3>

            <ProfileGrid>
              <div>
                <FieldGroup>
                  <FieldLabel>Название компании</FieldLabel>
                  <ProfileInput
                    type="text"
                    value={editableData.companyName}
                    onChange={(e) =>
                      handleInputChange("companyName", e.target.value)
                    }
                    placeholder="Введите название компании"
                  />
                </FieldGroup>

                <FieldGroup>
                  <FieldLabel>БИН</FieldLabel>
                  <ProfileInput
                    type="text"
                    value={editableData.bin}
                    readOnly
                    disabled
                    style={{
                      backgroundColor: "#f5f5f5",
                      cursor: "not-allowed",
                    }}
                    placeholder="Введите БИН"
                  />
                  <small style={{ color: "#6c757d", marginTop: "4px" }}>
                    БИН нельзя изменить
                  </small>
                </FieldGroup>

                <FieldGroup>
                  <FieldLabel>Юридический адрес</FieldLabel>
                  <ProfileInput
                    type="text"
                    value={editableData.legalAddress}
                    onChange={(e) =>
                      handleInputChange("legalAddress", e.target.value)
                    }
                    placeholder="Введите юридический адрес"
                  />
                </FieldGroup>
              </div>

              <div>
                <FieldGroup>
                  <FieldLabel>Фактический адрес</FieldLabel>
                  <ProfileInput
                    type="text"
                    value={editableData.actualAddress}
                    onChange={(e) =>
                      handleInputChange("actualAddress", e.target.value)
                    }
                    placeholder="Введите фактический адрес"
                  />
                </FieldGroup>

                <FieldGroup>
                  <FieldLabel>Сайт</FieldLabel>
                  <ProfileInput
                    type="url"
                    value={editableData.site}
                    onChange={(e) => handleInputChange("site", e.target.value)}
                    placeholder="Введите адрес сайта"
                  />
                </FieldGroup>

                <FieldGroup>
                  <FieldLabel>Факс</FieldLabel>
                  <ProfileInput
                    type="text"
                    value={editableData.fax}
                    onChange={(e) => handleInputChange("fax", e.target.value)}
                    placeholder="Введите факс"
                  />
                </FieldGroup>
              </div>
            </ProfileGrid>

            <FieldGroup>
              <FieldLabel>Описание</FieldLabel>
              <ProfileTextarea
                value={editableData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Введите описание компании"
              />
            </FieldGroup>
          </ProfileSection>

          {/* Кнопки действий */}
          <ButtonGroup>
            <SaveButton onClick={handleSave} disabled={!hasChanges || isSaving}>
              {isSaving ? "Сохранение..." : "Сохранить"}
            </SaveButton>
            <SubmitButton onClick={handleLogout}>Выйти из системы</SubmitButton>
          </ButtonGroup>
        </ProfileCard>
      </AuthContainer>
    </>
  );
};

const AuthPage = () => {
  const [activeTab, setActiveTab] = useState("login");
  const [showRegistrationWizard, setShowRegistrationWizard] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [showAuthNotification, setShowAuthNotification] = useState(false);
  const searchParams = useSearchParams();

  const { login, register, authError, isLoading, clearError, isAuthenticated } =
    useAuth();

  // Проверяем, нужно ли показать уведомление об авторизации
  useEffect(() => {
    const fromParam = searchParams.get("from");
    if (fromParam === "tender" || fromParam === "company") {
      setShowAuthNotification(true);
      // Автоматически скрываем уведомление через 5 секунд
      setTimeout(() => {
        setShowAuthNotification(false);
      }, 5000);
    }
  }, [searchParams]);

  // Функция для закрытия уведомления об авторизации
  const handleCloseAuthNotification = () => {
    setShowAuthNotification(false);
  };

  // Функция для получения текста уведомления
  const getNotificationText = () => {
    const fromParam = searchParams.get("from");
    if (fromParam === "company") {
      return "Заполните информацию о вашей компании";
    }
    return "Необходимо авторизоваться";
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Очищаем ошибку при изменении полей
    if (authError) {
      clearError();
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Очищаем форму при переключении табов
    setFormData({
      email: "",
      password: "",
      confirmPassword: "",
    });
    setRememberMe(false);
    clearError();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (activeTab === "login") {
      const success = await login(
        formData.email,
        formData.password,
        rememberMe
      );
      if (success) {
        // Можно перенаправить пользователя или показать успешное сообщение
        console.log("Успешный вход в систему");
      }
    } else {
      // Логика регистрации - сначала базовая регистрация
      const success = await register(
        formData.email,
        formData.password,
        formData.confirmPassword,
        rememberMe
      );
      if (success) {
        console.log("Успешная базовая регистрация, показываем мастер профиля");
        setShowRegistrationWizard(true);
      }
    }
  };

  // Обработчик завершения мастера регистрации
  const handleRegistrationComplete = (profileData) => {
    console.log("Профиль пользователя заполнен:", profileData);
    // Здесь будет API запрос для сохранения данных профиля
    setShowRegistrationWizard(false);
    // После сохранения профиля пользователь остается авторизованным
  };

  // Обработчик отмены мастера регистрации
  const handleRegistrationCancel = () => {
    setShowRegistrationWizard(false);
    // Пользователь остается авторизованным, но без заполненного профиля
  };

  // Если показываем мастер регистрации
  if (showRegistrationWizard) {
    return (
      <Layout>
        <AuthContainer>
          {/* Уведомление об авторизации для мастера регистрации */}
          {showAuthNotification && (
            <AuthNotification>
              <AuthNotificationText>
                {getNotificationText()}
              </AuthNotificationText>
              <AuthNotificationClose onClick={handleCloseAuthNotification}>
                ×
              </AuthNotificationClose>
            </AuthNotification>
          )}
          <RegistrationWizard
            initialEmail={formData.email}
            onComplete={handleRegistrationComplete}
            onCancel={handleRegistrationCancel}
          />
        </AuthContainer>
      </Layout>
    );
  }

  // Если пользователь авторизован, показываем личный кабинет
  if (isAuthenticated) {
    return (
      <Layout>
        {/* Уведомление об авторизации для авторизованных пользователей */}
        {showAuthNotification && (
          <AuthNotification>
            <AuthNotificationText>{getNotificationText()}</AuthNotificationText>
            <AuthNotificationClose onClick={handleCloseAuthNotification}>
              ×
            </AuthNotificationClose>
          </AuthNotification>
        )}
        <UserProfile />
      </Layout>
    );
  }

  return (
    <Layout>
      <AuthContainer>
        {/* Уведомление об авторизации */}
        {showAuthNotification && (
          <AuthNotification>
            <AuthNotificationText>{getNotificationText()}</AuthNotificationText>
            <AuthNotificationClose onClick={handleCloseAuthNotification}>
              ×
            </AuthNotificationClose>
          </AuthNotification>
        )}

        <AuthCard>
          <TabContainer>
            <Tab
              active={activeTab === "login"}
              onClick={() => handleTabChange("login")}
            >
              Вход
            </Tab>
            <Tab
              active={activeTab === "register"}
              onClick={() => handleTabChange("register")}
            >
              Регистрация
            </Tab>
          </TabContainer>

          <Form onSubmit={handleSubmit}>
            <InputField
              type="text"
              name="email"
              placeholder="Введите адрес электронной почты"
              value={formData.email}
              onChange={handleInputChange}
              required
            />

            <InputField
              type="password"
              name="password"
              placeholder={
                activeTab === "login" ? "Введите пароль" : "Придумайте пароль"
              }
              value={formData.password}
              onChange={handleInputChange}
              required
            />

            {activeTab === "register" && (
              <InputField
                type="password"
                name="confirmPassword"
                placeholder="Подтвердите пароль"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
              />
            )}

            <CheckboxContainer>
              <Checkbox
                type="checkbox"
                id="rememberMe"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <CheckboxLabel htmlFor="rememberMe">Запомнить меня</CheckboxLabel>
            </CheckboxContainer>

            {authError && <ErrorMessage>{authError}</ErrorMessage>}

            <SubmitButton type="submit" disabled={isLoading}>
              {isLoading
                ? "Загрузка..."
                : activeTab === "login"
                ? "Войти"
                : "Зарегистрироваться"}
            </SubmitButton>
          </Form>
        </AuthCard>
      </AuthContainer>
    </Layout>
  );
};

export default AuthPage;
