"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import DatePicker, { registerLocale } from "react-datepicker";
import { ru } from "date-fns/locale";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "../../context/AuthContext";
import authService from "../../services/auth.service";
import API_CONFIG from "../../config/api";

// Регистрируем русскую локализацию
registerLocale("ru", ru);

const TenderFormContainer = styled.div`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  min-height: 100vh;
  padding: 24px 20px;
`;
TenderFormContainer.displayName = "TenderFormContainer";

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;
ContentContainer.displayName = "ContentContainer";

const Header = styled.div`
  border-bottom: 1px solid #dfe4e5;
`;
Header.displayName = "Header";

const HeaderContent = styled.div`
  margin: 0 auto;
  max-width: 1150px;
  display: flex;
  padding: 22px 40px 16px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
`;
HeaderContent.displayName = "HeaderContent";

const BackButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  gap: 8px;
  padding: 8px 20px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
BackButton.displayName = "BackButton";

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;
ActionButtons.displayName = "ActionButtons";

const ClearAllButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: white;
  color: #434a54;
  border: 1px solid #d6dce1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
`;
ClearAllButton.displayName = "ClearAllButton";

const CreateTenderButton = styled.button`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 6px 14px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #0055b3;
  }
`;
CreateTenderButton.displayName = "CreateTenderButton";

const Title = styled.h1`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.5px;
  color: #434a54;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 28px;
  }
`;
Title.displayName = "Title";

const SectionTitle = styled.h2`
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 130%;
  color: #434a54;
  margin-top: 42px;
  margin-bottom: 24px;

  &:first-of-type {
    margin-top: 0;
  }
`;
SectionTitle.displayName = "SectionTitle";

const Text = styled.p`
  font-size: 17px;
  font-weight: 400;
  color: #434a54;
  margin-bottom: 16px;
  line-height: 1.5;
`;
Text.displayName = "Text";

const ProductFormCard = styled.div`
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
`;
ProductFormCard.displayName = "ProductFormCard";

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;
ProductHeader.displayName = "ProductHeader";

const ProductInfo = styled.div`
  flex: 1;
`;
ProductInfo.displayName = "ProductInfo";

const ProductId = styled.div`
  font-size: 17px;
  color: #969ea7;
  margin-bottom: 10px;
`;
ProductId.displayName = "ProductId";

const ProductTitle = styled.h3`
  font-size: 24px;
  font-weight: 400;
  color: #434a54;
  line-height: 32px;
  margin-bottom: 10px;
`;
ProductTitle.displayName = "ProductTitle";

const Label = styled.div`
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #969ea7;
  margin-bottom: 10px;
`;
Label.displayName = "Label";

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 36px;
  padding-right: 40px;

  &:focus {
    border-color: #0066cc;
  }
`;
Input.displayName = "Input";

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  padding: 10px 0;
  margin-bottom: 16px;
  align-items: flex-end;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;
FormRow.displayName = "FormRow";

const SmallFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  max-width: 200px;
  position: relative;
`;
SmallFormGroup.displayName = "SmallFormGroup";

const ClearAllButtonContainer = styled.div`
  display: flex;
  align-items: flex-start;
`;
ClearAllButtonContainer.displayName = "ClearAllButtonContainer";

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 75px;
  width: 100%;
  resize: vertical;
  color: #434a54;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    border-color: #0066cc;
  }
`;
TextArea.displayName = "TextArea";

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0;
  height: 36px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #0066cc;
  width: fit-content;
`;
ActionButtonContainer.displayName = "ActionButtonContainer";

const ActionButton = styled.button`
  background-color: ${(props) => (props.active ? "#0066cc" : "#f8f9fa")};
  color: ${(props) => (props.active ? "white" : "#434a54")};
  border: none;
  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${(props) => (props.active ? "#0056b3" : "#e9ecef")};
  }
`;
ActionButton.displayName = "ActionButton";

const NoProductsMessage = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
`;
NoProductsMessage.displayName = "NoProductsMessage";

const DatePickerContainer = styled.div`
  background: #f5f5f5;
  padding: 48px 0;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
DatePickerContainer.displayName = "DatePickerContainer";

const AddressContainer = styled.div`
  margin-bottom: 24px;
`;
AddressContainer.displayName = "AddressContainer";

const AddressRow = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;
AddressRow.displayName = "AddressRow";

const AddressInput = styled(Input)`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  padding-right: 16px;
`;
AddressInput.displayName = "AddressInput";

const CityDropdown = styled.div`
  position: relative;
  width: 300px;
`;
CityDropdown.displayName = "CityDropdown";

const CityButton = styled.button`
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #434a54;

  &:focus {
    border-color: #0066cc;
    outline: none;
  }

  &:hover {
    border-color: #0066cc;
  }
`;
CityButton.displayName = "CityButton";

const CityDropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
`;
CityDropdownList.displayName = "CityDropdownList";

const CityOption = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #434a54;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;
CityOption.displayName = "CityOption";

const CityCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${(props) => (props.checked ? "#0066cc" : "#ddd")};
  border-radius: 4px;
  background-color: ${(props) => (props.checked ? "#0066cc" : "white")};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
    opacity: ${(props) => (props.checked ? 1 : 0)};
  }
`;
CityCheckbox.displayName = "CityCheckbox";

const PublishButton = styled(CreateTenderButton)`
  font-size: 16px;
  padding: 16px 32px;
  margin-top: 32px;
`;
PublishButton.displayName = "PublishButton";

const TenderNameContainer = styled.div`
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  gap: 16px;
`;
TenderNameContainer.displayName = "TenderNameContainer";

const TenderInputContainer = styled.div`
  position: relative;
  flex: 1;
`;
TenderInputContainer.displayName = "TenderInputContainer";

const TenderNameInput = styled(Input)`
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 16px;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #e0e0e0;
  }
`;
TenderNameInput.displayName = "TenderNameInput";

// Стили для прикрепления файлов (общий блок)
const FileUploadSection = styled.div`
  margin-top: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
`;
FileUploadSection.displayName = "FileUploadSection";

const UploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #434a54;
  margin-bottom: 16px;

  &:hover {
    background-color: #f8f9fa;
  }
`;
UploadButton.displayName = "UploadButton";

const UploadText = styled.span`
  font-size: 14px;
  color: #434a54;
`;
UploadText.displayName = "UploadText";

const HiddenFileInput = styled.input`
  display: none;
`;
HiddenFileInput.displayName = "HiddenFileInput";

const AttachedFilesList = styled.div`
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;
AttachedFilesList.displayName = "AttachedFilesList";

const AttachedFileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
`;
AttachedFileItem.displayName = "AttachedFileItem";

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;
FileInfo.displayName = "FileInfo";

const FileName = styled.span`
  font-size: 14px;
  color: #333;
  font-weight: 500;
`;
FileName.displayName = "FileName";

const FileSize = styled.span`
  font-size: 12px;
  color: #666;
`;
FileSize.displayName = "FileSize";

const RemoveFileButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5c6cb;
  }
`;
RemoveFileButton.displayName = "RemoveFileButton";

const CalendarContainer = styled.div`
  .react-datepicker {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    font-family: inherit;
    background: white;
    padding: 32px;
  }

  .react-datepicker__header {
    background: white;
    border-bottom: none;
    padding: 0 0 20px 0;
  }

  .react-datepicker__current-month {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }

  .react-datepicker__navigation {
    top: 32px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      color: #e9ecef;
    }

    &--previous {
      left: 20px;
    }

    &--next {
      right: 20px;
    }
  }

  .react-datepicker__navigation-icon {
    &::before {
      border-color: #666;
      border-width: 2px 2px 0 0;
      width: 8px;
      height: 8px;
    }
  }

  .react-datepicker__day-names {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 10px;
  }

  .react-datepicker__day-name {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin: 0;
  }

  .react-datepicker__month {
  }

  .react-datepicker__week {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 0;
  }

  .react-datepicker__day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      background: #f0f8ff;
      color: #0066cc;
    }

    &--selected {
      background: #0066cc !important;
      color: white !important;
      font-weight: 600;
    }

    &--today {
      background: #e3f2fd;
      color: #0066cc;
      font-weight: 600;
    }

    &--weekend {
      color: #dc3545;
    }

    &--outside-month {
      color: #ccc;
    }

    &--disabled {
      color: #ccc;
      cursor: not-allowed;

      &:hover {
        background: transparent;
        color: #ccc;
      }
    }
  }

  .react-datepicker__triangle {
    display: none;
  }

  /* 📱 Медиа-запросы для адаптива */
  @media (max-width: 768px) {
    .react-datepicker {
      padding: 16px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .react-datepicker__current-month {
      font-size: 16px;
    }

    .react-datepicker__navigation {
      top: 24px;
      width: 22px;
      height: 22px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .react-datepicker {
      padding: 12px;
    }

    .react-datepicker__day,
    .react-datepicker__day-name {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }

    .react-datepicker__current-month {
      font-size: 14px;
    }

    .react-datepicker__navigation {
      top: 20px;
      width: 20px;
      height: 20px;
    }

    .react-datepicker__day-names,
    .react-datepicker__week {
      gap: 12px;
    }
  }
`;
CalendarContainer.displayName = "CalendarContainer";

const DeliverySection = styled.div`
  margin-bottom: 32px;
`;
DeliverySection.displayName = "DeliverySection";

const DeliveryText = styled.p`
  font-size: 14px;
  color: #969ea7;
  margin: 8px 0 16px 0;
  line-height: 1.5;
`;
DeliveryText.displayName = "DeliveryText";

// Хук для получения единиц измерения
const useUnits = () => {
  const [units, setUnits] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUnits = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const url = `${API_CONFIG.BASE_URL}/api/Units/Units`;
        console.log("Загрузка единиц измерения:", url);

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        setUnits(Array.isArray(result) ? result : []);
      } catch (err) {
        console.error("Ошибка при загрузке единиц измерения:", err);
        setError(err);
        setUnits([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUnits();
  }, []);

  return { units, isLoading, error };
};

// Хук для получения средних цен на работы
const useAveragePrices = (workIds) => {
  const [averagePrices, setAveragePrices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAveragePrices = async () => {
      if (!workIds || workIds.length === 0) {
        setAveragePrices([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Создаем URL с параметрами esnElementIds для каждой работы
        const params = workIds.map((id) => `esnElementIds=${id}`).join("&");
        const url = `${API_CONFIG.BASE_URL}/api/BaseWorks/Adverts/Average?${params}`;

        console.log("Загрузка средних цен на работы:", url);

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        setAveragePrices(Array.isArray(result) ? result : []);
      } catch (err) {
        console.error("Ошибка при загрузке средних цен:", err);
        setError(err);
        setAveragePrices([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAveragePrices();
  }, [workIds]);

  return { averagePrices, isLoading, error };
};

const WorkTenderFormClient = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [tenderName, setTenderName] = useState("");
  const [deliveryDate, setDeliveryDate] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [selectedCityId, setSelectedCityId] = useState("02"); // По умолчанию Алматы
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [withVAT, setWithVAT] = useState(true); // true - с НДС, false - без НДС
  const [isPublishing, setIsPublishing] = useState(false);

  // Общие файлы для всего тендера
  const [tenderFiles, setTenderFiles] = useState([]);

  // Хук для получения единиц измерения
  const { units, isLoading: isUnitsLoading, error: unitsError } = useUnits();

  // Список городов
  const cities = [
    {
      RegionId: "01",
      RegionName: "Нур-Султан",
      LivingWage: 22702,
      Coefficient: 1.155,
    },
    {
      RegionId: "02",
      RegionName: "Алматы",
      LivingWage: 22283,
      Coefficient: 1.134,
    },
    {
      RegionId: "04",
      RegionName: "Актюбинская область",
      LivingWage: 18010,
      Coefficient: 0.917,
    },
    {
      RegionId: "05",
      RegionName: "Алматинская область",
      LivingWage: 20557,
      Coefficient: 1.046,
    },
    {
      RegionId: "06",
      RegionName: "Атырауская область",
      LivingWage: 20297,
      Coefficient: 1.033,
    },
    {
      RegionId: "07",
      RegionName: "Западно-Казахстанская область",
      LivingWage: 17947,
      Coefficient: 0.913,
    },
    {
      RegionId: "08",
      RegionName: "Жамбылская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "09",
      RegionName: "Карагандинская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "10",
      RegionName: "Костанайская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "11",
      RegionName: "Кызылординская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "12",
      RegionName: "Мангистауская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "13",
      RegionName: "Туркестанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "14",
      RegionName: "Павлодарская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "15",
      RegionName: "Северо-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "16",
      RegionName: "Восточно-Казахстанская область",
      LivingWage: null,
      Coefficient: null,
    },
    {
      RegionId: "17",
      RegionName: "Акмолинская область",
      LivingWage: 18246,
      Coefficient: 0.929,
    },
    {
      RegionId: "3 ",
      RegionName: "Шымкент",
      LivingWage: 20283,
      Coefficient: 1,
    },
  ];

  // Получаем работы из localStorage
  const [selectedWorks, setSelectedWorks] = useState([]);
  const [formData, setFormData] = useState([]);
  const [averagePrices, setAveragePrices] = useState([]);

  // Функция для получения единицы измерения по ID
  const getUnitById = (unitId) => {
    const unit = units.find((u) => u.IdUnit === unitId);
    return unit ? unit.ShortNameOfUnit : "ед.";
  };

  // Функция для получения средней цены для работы
  const getAveragePrice = (workId) => {
    const workPrices = averagePrices.filter(
      (price) => price.EsnElementId === workId
    );
    if (workPrices.length === 0) return null;

    const totalPrice = workPrices.reduce(
      (sum, price) => sum + (price.RetailPrice || 0),
      0
    );
    const averagePrice = totalPrice / workPrices.length;

    return Math.round(averagePrice); // Округляем до целого числа
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem("selectedTenderWorks");
        const works = saved ? JSON.parse(saved) : [];
        setSelectedWorks(works);

        // Загружаем средние цены на работы
        const savedPrices = localStorage.getItem("workTenderAveragePrices");
        const prices = savedPrices ? JSON.parse(savedPrices) : [];
        setAveragePrices(prices);

        // Инициализируем форму для каждой работы
        setFormData(
          works.map((work) => {
            // Ищем среднюю цену для этой работы
            const workPrices = prices.filter(
              (price) => price.EsnElementId === work.IdBaseWork
            );
            let averagePrice = "";
            if (workPrices.length > 0) {
              const totalPrice = workPrices.reduce(
                (sum, price) => sum + (price.RetailPrice || 0),
                0
              );
              averagePrice = Math.round(totalPrice / workPrices.length);
            }

            return {
              workId: work.CodeOfBaseWork,
              workName: work.TitleOfBaseWork,
              workCode: work.CodeOfBaseWork,
              workUnit: work.IdUnitOfBaseWork,
              idBaseWork: work.IdBaseWork, // Добавляем IdBaseWork для запроса цен
              quantity: "",
              maxPrice: averagePrice, // Автоматически заполняем средней ценой
              description: "",
              analogAllowed: true,
            };
          })
        );
      } catch (error) {
        console.error("Ошибка при загрузке данных из localStorage:", error);
      }
    }
  }, []);

  // Закрытие dropdown при клике вне его
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isCityDropdownOpen && !event.target.closest("[data-city-dropdown]")) {
        setIsCityDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCityDropdownOpen]);

  const handleFormDataChange = (index, field, value) => {
    setFormData((prev) => {
      const newData = [...prev];
      newData[index] = { ...newData[index], [field]: value };
      return newData;
    });
  };

  // Функции для работы с dropdown городов
  const toggleCityDropdown = () => {
    setIsCityDropdownOpen(!isCityDropdownOpen);
  };

  const handleCitySelect = (selectedCity) => {
    setSelectedCityId(selectedCity.RegionId);
    setIsCityDropdownOpen(false);
  };

  // Получаем текущий выбранный город
  const getCurrentCity = () => {
    return cities.find((city) => city.RegionId === selectedCityId) || cities[1]; // По умолчанию Алматы
  };

  const handleRemoveWork = (index) => {
    const removedWorkCode = formData[index].workId;

    // Удаляем из formData
    setFormData((prev) => prev.filter((_, i) => i !== index));

    // Обновляем selectedWorks
    const updatedWorks = selectedWorks.filter(
      (work) => work.CodeOfBaseWork !== removedWorkCode
    );
    setSelectedWorks(updatedWorks);

    // Обновляем localStorage
    try {
      localStorage.setItem("selectedTenderWorks", JSON.stringify(updatedWorks));

      // Если это была последняя работа, перенаправляем на CreateWorkTender
      if (updatedWorks.length === 0) {
        router.push("/create-work-tender");
        return;
      }
    } catch (error) {
      console.error("Ошибка при обновлении localStorage:", error);
    }
  };

  const handleSubmit = async () => {
    if (isPublishing) return; // Предотвращаем повторные нажатия

    // Валидация обязательных полей
    if (!tenderName.trim()) {
      alert("Укажите название тендера");
      return;
    }

    if (!deliveryDate) {
      alert("Укажите дату выполнения работ");
      return;
    }

    if (!deliveryAddress.trim()) {
      alert("Укажите адрес выполнения работ");
      return;
    }

    // Проверяем, что все работы имеют количество
    const incompleteWorks = formData.filter(
      (work) => !work.quantity || work.quantity <= 0
    );
    if (incompleteWorks.length > 0) {
      alert("Укажите объем для всех работ");
      return;
    }

    // Проверяем авторизацию и данные компании
    if (
      !user?.userId ||
      user?.userId === "00000000-0000-0000-0000-000000000000" ||
      !user?.companyId ||
      user?.companyId === "0000" ||
      user?.companyId === 0
    ) {
      router.push("/auth?from=company");
      return;
    }

    try {
      setIsPublishing(true);
      console.log("🚀 Начинаем создание тендера на работы...");
      console.log("👤 Пользователь:", user);
      console.log("📝 Данные формы:", {
        tenderName,
        deliveryDate,
        deliveryAddress,
        selectedCityId,
        withVAT,
        formData: formData.length,
        selectedWorks: selectedWorks.length,
      });

      // Пока только выводим в консоль (без POST запросов)
      alert("Тендер на работы будет создан (функционал в разработке)");
    } catch (error) {
      console.error("❌ Ошибка при создании тендера:", error);
      alert(`Ошибка при создании тендера: ${error.message}`);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleClearAll = () => {
    // Очищаем localStorage
    try {
      localStorage.removeItem("selectedTenderWorks");
      localStorage.removeItem("workTenderAveragePrices");
    } catch (error) {
      console.error("Ошибка при очистке localStorage:", error);
    }

    // Перенаправляем на CreateWorkTender
    router.push("/create-work-tender");
  };

  const handleBack = () => {
    router.back();
  };

  const handleVATChoice = (includeVAT) => {
    setWithVAT(includeVAT);
  };

  const handleAnalogChoice = (index, analogAllowed) => {
    handleFormDataChange(index, "analogAllowed", analogAllowed);
  };

  // Функции для работы с файлами тендера
  const handleTenderFileUpload = (files) => {
    if (files && files.length > 0) {
      const fileArray = Array.from(files);

      // Проверяем размер файлов (максимум 10MB на файл)
      const maxSize = 10 * 1024 * 1024; // 10MB
      const oversizedFiles = fileArray.filter((file) => file.size > maxSize);

      if (oversizedFiles.length > 0) {
        alert(
          `Файлы слишком большие. Максимальный размер: 10MB\n${oversizedFiles
            .map((f) => f.name)
            .join("\n")}`
        );
        return;
      }

      setTenderFiles((prev) => [...prev, ...fileArray]);
    }
  };

  const handleRemoveTenderFile = (fileIndex) => {
    setTenderFiles((prev) => prev.filter((_, index) => index !== fileIndex));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (selectedWorks.length === 0) {
    return (
      <TenderFormContainer>
        <ContentContainer>
          <Title>Нет выбранных работ для создания тендера</Title>
          <NoProductsMessage>
            Сначала выберите работы на странице создания тендера
          </NoProductsMessage>
          <BackButton onClick={() => router.push("/create-work-tender")}>
            ПЕРЕЙТИ К ВЫБОРУ РАБОТ
          </BackButton>
        </ContentContainer>
      </TenderFormContainer>
    );
  }

  return (
    <>
      <Header>
        <HeaderContent>
          <BackButton onClick={handleBack}>
            <img src="/icons/arrow_back_24px.svg" alt="Назад" />
            ВЕРНУТЬСЯ К ПОИСКУ
          </BackButton>

          <ActionButtons>
            <ClearAllButton onClick={handleClearAll}>
              <img
                src="/icons/BusketCreateTender.svg"
                width={"13"}
                height={"13"}
                alt="Очистить"
              />
              ОЧИСТИТЬ ВСЕ
            </ClearAllButton>
          </ActionButtons>
        </HeaderContent>
      </Header>

      <TenderFormContainer>
        <ContentContainer>
          <Title>Создание тендера на выполнение работ</Title>

          <SectionTitle>Детали тендера</SectionTitle>
          <Text>
            Укажите необходимый объем работ, цену которую готовы <br />
            предложить и пожелания к выполнению.
          </Text>

          {formData.map((work, index) => (
            <ProductFormCard key={work.workId}>
              <ProductHeader>
                <ProductInfo>
                  <ProductId>Код: {work.workCode}</ProductId>
                  <Label>Ед.изм: {getUnitById(work.workUnit)}</Label>
                  <ProductTitle>{work.workName}</ProductTitle>

                  <Label style={{ marginBottom: 0 }}>
                    Выполняется квалифицированными специалистами с
                    использованием современного оборудования и технологий.
                  </Label>
                </ProductInfo>
                <ClearAllButtonContainer>
                  <ClearAllButton
                    onClick={() => handleRemoveWork(index)}
                    style={{ padding: "12px" }}
                  >
                    <img
                      src="/icons/BusketCreateTender.svg"
                      width={"16"}
                      height={"16"}
                      alt="Удалить"
                    />
                  </ClearAllButton>
                </ClearAllButtonContainer>
              </ProductHeader>

              <FormRow>
                <SmallFormGroup>
                  <Input
                    type="number"
                    value={work.quantity}
                    onChange={(e) =>
                      handleFormDataChange(index, "quantity", e.target.value)
                    }
                    placeholder="Объем"
                    required
                  />
                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    {getUnitById(work.workUnit)}
                  </span>
                </SmallFormGroup>

                <SmallFormGroup>
                  <Input
                    type="number"
                    value={work.maxPrice}
                    onChange={(e) =>
                      handleFormDataChange(index, "maxPrice", e.target.value)
                    }
                    placeholder={
                      getAveragePrice(work.idBaseWork)
                        ? `Средняя цена: ${getAveragePrice(work.idBaseWork)} ₸`
                        : "Ваша цена"
                    }
                    style={{ paddingRight: "77px" }}
                  />

                  <span
                    style={{
                      position: "absolute",
                      right: "8px",
                      bottom: "8px",
                      color: "#656D78",
                      fontSize: "17px",
                      fontWeight: "400",
                    }}
                  >
                    ₸ за {getUnitById(work.workUnit)}
                  </span>
                </SmallFormGroup>

                {/* <ActionButtonContainer>
                  <ActionButton
                    active={work.analogAllowed === true}
                    onClick={() => handleAnalogChoice(index, true)}
                  >
                    Можно аналог
                  </ActionButton>
                  <ActionButton
                    active={work.analogAllowed === false}
                    onClick={() => handleAnalogChoice(index, false)}
                  >
                    Только это
                  </ActionButton>
                </ActionButtonContainer> */}
              </FormRow>

              <Label style={{ fontSize: "17px", color: "#656D78" }}>
                Пожелания к выполнению работ
              </Label>

              <TextArea
                value={work.description}
                onChange={(e) =>
                  handleFormDataChange(index, "description", e.target.value)
                }
                placeholder="Дополнительные требования к выполнению работ"
              />
            </ProductFormCard>
          ))}

          <DeliverySection>
            <SectionTitle>Учесть НДС в предложениях?</SectionTitle>
            <DeliveryText>
              Мы предупредим подрядчиков о необходимости подачи цены
              <br />с учетом или без учета НДС.
            </DeliveryText>

            <ActionButtonContainer>
              <ActionButton
                active={withVAT === true}
                onClick={() => handleVATChoice(true)}
              >
                С НДС
              </ActionButton>
              <ActionButton
                active={withVAT === false}
                onClick={() => handleVATChoice(false)}
              >
                Без НДС
              </ActionButton>
            </ActionButtonContainer>
          </DeliverySection>

          <DatePickerContainer>
            <SectionTitle>Срок выполнения работ</SectionTitle>
            <Text>
              Укажите дату когда необходимо завершить выполнение работ
            </Text>

            <CalendarContainer>
              <DatePicker
                selected={deliveryDate}
                onChange={(date) => setDeliveryDate(date)}
                dateFormat="dd MMMM yyyy"
                locale="ru"
                calendarStartDay={1}
                minDate={new Date()}
                placeholderText="Выберите дату"
                inline
              />
            </CalendarContainer>
          </DatePickerContainer>

          <AddressContainer>
            <SectionTitle>Адрес выполнения работ</SectionTitle>
            <Text>Укажите адрес где необходимо выполнить работы</Text>

            <AddressRow>
              <TenderInputContainer>
                <Label>Название улицы, дом, строение</Label>
                <AddressInput
                  type="text"
                  value={deliveryAddress}
                  onChange={(e) => setDeliveryAddress(e.target.value)}
                  placeholder="Укажите адрес выполнения работ"
                  required
                />
              </TenderInputContainer>

              <div>
                <Label>Город</Label>
                <CityDropdown data-city-dropdown>
                  <CityButton onClick={toggleCityDropdown} type="button">
                    {getCurrentCity().RegionName}
                    <span
                      style={{
                        transform: isCityDropdownOpen
                          ? "rotate(180deg)"
                          : "rotate(0deg)",
                        transition: "transform 0.2s ease",
                      }}
                    >
                      ▼
                    </span>
                  </CityButton>
                  {isCityDropdownOpen && (
                    <CityDropdownList>
                      {cities.map((cityOption) => (
                        <CityOption
                          key={cityOption.RegionId}
                          onClick={() => handleCitySelect(cityOption)}
                        >
                          <span>{cityOption.RegionName}</span>
                          <CityCheckbox
                            checked={selectedCityId === cityOption.RegionId}
                          />
                        </CityOption>
                      ))}
                    </CityDropdownList>
                  )}
                </CityDropdown>
              </div>
            </AddressRow>
          </AddressContainer>

          <SectionTitle>Название тендера</SectionTitle>
          <Text>
            Придумайте название тендера для удобства в списке ваших тендеров
          </Text>

          <TenderNameContainer>
            <TenderInputContainer>
              <Label>Название тендера</Label>
              <TenderNameInput
                type="text"
                value={tenderName}
                onChange={(e) => setTenderName(e.target.value)}
                placeholder="Название тендера"
                required
              />
            </TenderInputContainer>
          </TenderNameContainer>

          {/* Общий блок для прикрепления файлов */}
          <FileUploadSection>
            <SectionTitle style={{ marginTop: 0 }}>
              Прикрепить файлы к тендеру
            </SectionTitle>
            <Text>
              Загрузите дополнительные документы, чертежи, спецификации или
              другие файлы, которые помогут подрядчикам лучше понять требования
              к работам.
            </Text>

            <UploadButton
              onClick={() =>
                document.getElementById("tender-file-input").click()
              }
            >
              <img src="/icons/Upload.svg" alt="Загрузить" />
              <UploadText>Прикрепить файлы</UploadText>
            </UploadButton>

            <HiddenFileInput
              id="tender-file-input"
              type="file"
              multiple
              onChange={(e) => handleTenderFileUpload(e.target.files)}
            />

            {tenderFiles.length > 0 && (
              <AttachedFilesList>
                {tenderFiles.map((file, fileIndex) => (
                  <AttachedFileItem key={fileIndex}>
                    <FileInfo>
                      <FileName>{file.name}</FileName>
                      <FileSize>{formatFileSize(file.size)}</FileSize>
                    </FileInfo>
                    <RemoveFileButton
                      onClick={() => handleRemoveTenderFile(fileIndex)}
                      title="Удалить файл"
                    >
                      ×
                    </RemoveFileButton>
                  </AttachedFileItem>
                ))}
              </AttachedFilesList>
            )}
          </FileUploadSection>

          <div style={{ textAlign: "center" }}>
            <PublishButton
              onClick={handleSubmit}
              disabled={isPublishing}
              style={{
                opacity: isPublishing ? 0.5 : 1,
                cursor: isPublishing ? "not-allowed" : "pointer",
              }}
            >
              {isPublishing ? "ПУБЛИКАЦИЯ..." : "ОПУБЛИКОВАТЬ ТЕНДЕР"}
              <img
                src="/icons/CheckCreateTender.svg"
                width={"15"}
                height={"15"}
                alt="Опубликовать"
              />
            </PublishButton>
          </div>
        </ContentContainer>
      </TenderFormContainer>
    </>
  );
};

export default WorkTenderFormClient;
