import { Suspense } from "react";
import Layout from "../../components/Layout";
import WorkTenderFormClient from "./WorkTenderFormClient";
import { ISRUtils } from "../../config/isr";

// Генерация мета-тегов для SEO
export async function generateMetadata() {
  return {
    title:
      "Форма создания тендера на работы | SADI Shop - детали тендера на выполнение работ",
    description:
      "Заполните форму создания тендера на выполнение строительных и монтажных работ. Укажите объемы, цены, сроки выполнения, адрес и требования к подрядчикам в Казахстане.",
    keywords: [
      "SADI Shop",
      "САДИ Шоп",
      "sadi",
      "сади",
      "форма тендера на работы",
      "создание тендера работы",
      "детали тендера",
      "объем работ",
      "цена работ",
      "сроки выполнения",
      "требования к работам",
      "техническое задание",
      "спецификация работ",
      "строительные работы",
      "монтажные работы",
      "отделочные работы",
      "ремонтные работы",
      "подрядчики",
      "исполнители",
      "строительные компании",
      "бригады",
      "мастера",
      "специалисты",
      "квалифицированные рабочие",
      "опытные подрядчики",
      "надежные исполнители",
      "проверенные компании",
      "лицензированные подрядчики",
      "сертифицированные специалисты",
      "гарантия качества",
      "гарантийные обязательства",
      "страхование работ",
      "ответственность подрядчика",
      "контроль качества",
      "приемка работ",
      "акт выполненных работ",
      "смета работ",
      "калькуляция",
      "расценки",
      "единицы измерения",
      "нормы времени",
      "трудозатраты",
      "материальные затраты",
      "накладные расходы",
      "плановая прибыль",
      "НДС",
      "налоги",
      "социальные взносы",
      "себестоимость работ",
      "рыночная стоимость",
      "конкурентные цены",
      "оптимальная цена",
      "экономия средств",
      "бюджет проекта",
      "финансирование",
      "оплата работ",
      "авансовые платежи",
      "промежуточные платежи",
      "окончательный расчет",
      "безналичный расчет",
      "наличный расчет",
      "банковский перевод",
      "электронные деньги",
      "криптовалюта",
      "бартер",
      "взаимозачет",
      "отсрочка платежа",
      "рассрочка",
      "кредитование",
      "лизинг",
      "факторинг",
      "форфейтинг",
      "аккредитив",
      "банковская гарантия",
      "поручительство",
      "залог",
      "задаток",
      "обеспечительный платеж",
      "неустойка",
      "штрафы",
      "пени",
      "компенсация ущерба",
      "возмещение убытков",
      "страховое возмещение",
      "форс-мажор",
      "непреодолимая сила",
      "чрезвычайные обстоятельства",
      "стихийные бедствия",
      "техногенные катастрофы",
      "социальные потрясения",
      "военные действия",
      "террористические акты",
      "эпидемии",
      "карантин",
      "локдаун",
      "ограничения",
      "запреты",
      "разрешения",
      "лицензии",
      "сертификаты",
      "допуски",
      "аккредитации",
      "аттестации",
      "квалификационные свидетельства",
      "профессиональные навыки",
      "опыт работы",
      "портфолио",
      "рекомендации",
      "отзывы",
      "рейтинг",
      "репутация",
      "надежность",
      "добросовестность",
      "исполнительность",
      "пунктуальность",
      "качество работ",
      "скорость выполнения",
      "соблюдение сроков",
      "выполнение обязательств",
      "договорная дисциплина",
      "деловая этика",
      "профессионализм",
      "компетентность",
      "экспертиза",
      "консультации",
      "техническая поддержка",
      "сервисное обслуживание",
      "послегарантийное обслуживание",
      "техническое обслуживание",
      "регламентные работы",
      "профилактические работы",
      "диагностика",
      "ремонт",
      "модернизация",
      "реконструкция",
      "капитальный ремонт",
      "текущий ремонт",
      "аварийный ремонт",
      "срочный ремонт",
      "плановый ремонт",
      "внеплановый ремонт",
      "Казахстан",
      "Астана",
      "Нур-Султан",
      "Алматы",
      "Шымкент",
      "Караганда",
      "Актобе",
      "Тараз",
      "Павлодар",
      "Усть-Каменогорск",
      "Семей",
      "Атырау",
      "Костанай",
      "Кызылорда",
      "Уральск",
      "Петропавловск",
      "Актау",
      "Темиртау",
      "Туркестан",
      "Кокшетау",
      "Талдыкорган",
      "Экибастуз",
      "Рудный",
      "Жезказган",
      "Балхаш",
    ],
    openGraph: {
      title: "Форма создания тендера на работы",
      description:
        "Заполните детали тендера на выполнение строительных и монтажных работ. Укажите объемы, цены и требования.",
      type: "website",
      url: "https://shop.sadi.kz/work-tender-form",
      siteName: "SADI.KZ",
      images: [
        {
          url: "/images/work-tender-form-og.jpg",
          width: 1200,
          height: 630,
          alt: "Форма создания тендера на работы",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: "Форма создания тендера на работы",
      description:
        "Заполните детали тендера на выполнение строительных и монтажных работ.",
      images: ["/images/work-tender-form-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: "https://shop.sadi.kz/work-tender-form",
    },
  };
}

// Структурированные данные для поисковых систем
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Форма создания тендера на работы",
  description:
    "Заполните форму создания тендера на выполнение строительных и монтажных работ",
  url: "https://shop.sadi.kz/work-tender-form",
  mainEntity: {
    "@type": "Service",
    name: "Форма тендера на работы",
    description:
      "Форма для детального описания тендера на выполнение строительных и монтажных работ",
    provider: {
      "@type": "Organization",
      name: "SADI.KZ",
      url: "https://shop.sadi.kz",
    },
    serviceType: "Форма электронных торгов на работы",
    areaServed: {
      "@type": "Country",
      name: "Казахстан",
    },
  },
  breadcrumb: {
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "Главная",
        item: "https://shop.sadi.kz",
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Создание тендера на работы",
        item: "https://shop.sadi.kz/create-work-tender",
      },
      {
        "@type": "ListItem",
        position: 3,
        name: "Форма тендера",
        item: "https://shop.sadi.kz/work-tender-form",
      },
    ],
  },
};

// Компонент загрузки
function WorkTenderFormLoading() {
  return (
    <div
      style={{
        padding: "40px 20px",
        textAlign: "center",
        minHeight: "400px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div>
        <h1>Создание тендера на работы</h1>
        <p>Загрузка формы тендера...</p>
        <div
          style={{
            width: "40px",
            height: "40px",
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #0066cc",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "20px auto",
          }}
        />
      </div>
    </div>
  );
}

// ISR: Кэширование страницы формы тендера на работы на 1 день
export const revalidate = 86400; // 1 день в секундах

// Основной серверный компонент
export default function WorkTenderFormPage() {
  // Логируем ISR операцию
  ISRUtils.logISROperation("work-tender-form-page", {
    revalidate: 86400,
    note: "Страница формы создания тендера на работы с ISR кэшированием",
  });

  return (
    <Layout>
      {/* Структурированные данные */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* SEO-контент для поисковых систем */}
      <div style={{ display: "none" }}>
        <h1>
          Форма создания тендера на выполнение строительных и монтажных работ
        </h1>
        <h2>Детали тендера на работы</h2>
        <p>
          Заполните подробную форму создания тендера на выполнение работ.
          Укажите объемы работ, желаемые цены, сроки выполнения, адрес объекта и
          дополнительные требования к подрядчикам. Наша система поможет найти
          квалифицированных исполнителей для ваших задач.
        </p>
        <h3>Что нужно указать в форме тендера</h3>
        <ul>
          <li>Объем работ в соответствующих единицах измерения</li>
          <li>Максимальную цену за единицу работы</li>
          <li>Возможность выполнения аналогичных работ</li>
          <li>Требования по НДС</li>
          <li>Сроки выполнения работ</li>
          <li>Адрес выполнения работ</li>
          <li>Название тендера</li>
          <li>Дополнительные файлы и документы</li>
        </ul>
        <h3>Типы работ в тендере</h3>
        <p>
          Строительные работы, монтажные работы, отделочные работы, ремонтные
          работы, электромонтажные работы, сантехнические работы, кровельные
          работы, фасадные работы.
        </p>
      </div>

      {/* Клиентский компонент с Suspense */}
      <Suspense fallback={<WorkTenderFormLoading />}>
        <WorkTenderFormClient />
      </Suspense>
    </Layout>
  );
}
